// components/TabOrders.tsx
"use client";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { useConfirmation } from "@/contexts/ConfirmationContext";
import ConfirmationDialog from "./ConfirmationDialog";
import AlertDialog from "./AlertDialog";
import { useAlert } from "@/hooks/useAlert";
import { GROUP_CONFIGS, GroupConfig, getSymbolPoint, USD_TO_THB_RATE, formatNumber, formatNumberBySymbol, calculateRiskReward, calculateRiskRewardWithMagic } from "@/config/global";
import { formatProfitLoss } from "@/utils/formatters";
import { useAppSettings } from '@/contexts/AppSettingsContext';

// USD to THB exchange rate (approximate - you can update this or make it dynamic)
// const USD_TO_THB_RATE = 32.5;

// Using proxy API routes instead of direct webhook URLs

interface Position {
  ticket: number;
  symbol: string;
  type: "BUY" | "SELL";
  volume: number;
  entry: number;
  current: number;
  sl: number | null;
  tp: number | null;
  profit: number;
  comment: string;
  magic?: number; // Magic number from webhook for RR calculation
}

interface PendingOrder {
  ticket: number;
  symbol: string;
  type: string;
  volume: number;
  entry: number;
  current: number;
  sl: number | null;
  tp: number | null;
  comment: string;
}

interface GroupData {
  [groupKey: string]: {
    group_id: string;
    positions: Position[];
    pending: PendingOrder[];
    count: number;
  };
}

interface OrdersData {
  error: boolean;
  message?: string;
  data?: {
    positions: Position[];
    pending: PendingOrder[];
    zd_groups: GroupData;
    in_groups: GroupData;
    timestamp: number;
  };
}

export default function TabOrders({
  customStyle,
  sendWebhook,
  loading,
}: {
  customStyle: Object;
  sendWebhook: Function;
  loading: boolean;
}) {
  const pathname = usePathname();
  const [ordersData, setOrdersData] = useState<OrdersData | null>(null);
  const [activeSubTab, setActiveSubTab] = useState<string>("orders");
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Alert hook for beautiful dialogs
  const { alert, showSuccess, showError, closeAlert } = useAlert();

  // Get app settings for currency display
  const { settings } = useAppSettings();

  // Confirmation dialog state
  const { requireConfirmation } = useConfirmation();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [pendingAction, setPendingAction] = useState<{
    action: string;
    group_type?: string;
    group_id?: string;
    ticket?: number;
    orderType?: 'position' | 'pending';
  } | null>(null);

  // Auto-refresh every 5 seconds (when enabled)
  useEffect(() => {
    fetchOrdersData();
    if (autoRefresh) {
      const interval = setInterval(fetchOrdersData, 5000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const fetchOrdersData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const res = await fetch('/api/orders/data', {
        headers: {
          'x-current-path': pathname
        }
      });
      // if (!response.ok) {
      //   throw new Error(`HTTP error! status: ${response.status}`);
      // }

      // Log response status for debugging
      // setLogs((prev) => [`📊 Proxy Response Status: ${res.status} ${res.statusText}`, ...prev]);

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status} ${res.statusText}`);
      }
      // const data: OrdersData = await response.json();
      const result = await res.json();
      if (result.error) {
        throw new Error(result.message || "Failed to fetch orders data");
      }
      const data: OrdersData = result.data

      setOrdersData(data);
      setLastUpdate(new Date());
    } catch (err) {
      console.error("Failed to fetch orders data:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch orders data");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloseGroup = (group_type: "ZD" | "IN", group_id: string) => {
    const action = {
      action: "close_group",
      group_type: group_type,
      group_id: group_id,
    };

    if (requireConfirmation) {
      setPendingAction(action);
      setShowConfirmation(true);
    } else {
      executeCloseGroup(action);
    }
  };

  const executeCloseGroup = async (action: { action: string; group_type: string; group_id: string }) => {
    try {
      const response = await fetch('/api/orders/action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-current-path': pathname
        },
        body: JSON.stringify(action),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.error) {
        throw new Error(result.message || "Failed to close group");
      }

      showSuccess(`Successfully closed ${action.group_type} group: ${action.group_id}`);
      // Refresh data after successful action
      fetchOrdersData();
    } catch (err) {
      console.error("Failed to close group:", err);
      showError(`Failed to close group: ${err instanceof Error ? err.message : "Unknown error"}`);
    }
  };

  const handleCloseOrder = (ticket: number, orderType: 'position' | 'pending') => {
    const action = {
      action: orderType === 'position' ? "close_position" : "cancel_order",
      ticket: ticket,
      orderType: orderType,
    };

    if (requireConfirmation) {
      setPendingAction(action);
      setShowConfirmation(true);
    } else {
      executeCloseOrder(action);
    }
  };

  const executeCloseOrder = async (action: { action: string; ticket: number; orderType: 'position' | 'pending' }) => {
    try {
      const response = await fetch('/api/orders/action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(action),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.error) {
        throw new Error(result.message || `Failed to ${action.action}`);
      }

      showSuccess(`Successfully ${action.action === 'close_position' ? 'closed position' : 'cancelled order'}: ${action.ticket}`);
      // Refresh data after successful action
      fetchOrdersData();
    } catch (err) {
      console.error(`Failed to ${action.action}:`, err);
      showError(`Failed to ${action.action}: ${err instanceof Error ? err.message : "Unknown error"}`);
    }
  };

  const handleConfirmSubmit = () => {
    if (pendingAction) {
      if (pendingAction.action === 'close_group') {
        executeCloseGroup(pendingAction as { action: string; group_type: string; group_id: string });
      } else {
        executeCloseOrder(pendingAction as { action: string; ticket: number; orderType: 'position' | 'pending' });
      }
      setPendingAction(null);
    }
    setShowConfirmation(false);
  };

  const handleCancelSubmit = () => {
    setPendingAction(null);
    setShowConfirmation(false);
  };


 
  // Calculate total profit for positions
  const calculateTotalProfit = (positions: Position[]) => {
    return positions.reduce((total, pos) => total + pos.profit, 0);
  }; 
  
  // Calculate profit/loss for TP or SL
  const calculateProfitLoss = (entryPrice: number, targetPrice: number, volume: number, symbol: string, orderType: string): number => {
    if (entryPrice === 0 || targetPrice === 0) return 0;

    const cleanSymbol = symbol.split('.')[0];
    const isBuyAction = orderType.toLowerCase().includes('buy');
    const symbolPoint = getSymbolPoint(cleanSymbol);

    // Calculate price difference in points
    let priceDiff: number;
    if (isBuyAction) {
      priceDiff = targetPrice - entryPrice; // Positive for profit, negative for loss
    } else {
      priceDiff = entryPrice - targetPrice; // Positive for profit, negative for loss
    }

    // Convert to points and calculate profit/loss
    const pointsProfit = priceDiff / symbolPoint;

    // Standard lot value calculation (varies by symbol type)
    // Point value represents the dollar value per point per mini lot
    let pointValue: number;
    if (cleanSymbol.includes('JPY')) {
      pointValue = 0.01; // For JPY pairs, 1 point = $0.01 per mini lot
    } else if (cleanSymbol === 'XAUUSD') {
      pointValue = 0.01; // For Gold, 1 point = $0.01 per mini lot
    } else if (cleanSymbol.includes('USD')) {
      pointValue = 0.0001; // For major USD pairs, 1 point = $0.0001 per mini lot
    } else {
      pointValue = 0.0001; // Default for other pairs
    }

    // Calculate profit/loss in USD
    const profitLoss = pointsProfit * pointValue * (volume * 100); // volume is in mini lots

    return profitLoss;
  };

  // Dynamic helper functions for different group types
  const getGroupData = (groupId: string) => {
    if (!ordersData?.data) return { positions: [], pending: [], groups: {}, count: 0 };

    const groupConfig = GROUP_CONFIGS.find(config => config.id === groupId);
    if (!groupConfig) return { positions: [], pending: [], groups: {}, count: 0 };

    if (groupConfig.requiresGroupId) {
      // For grouped data (zd, in)
      const groupsData = groupId === 'zd' ? ordersData.data.zd_groups : ordersData.data.in_groups;
      return {
        positions: [],
        pending: [],
        groups: groupsData || {},
        count: Object.keys(groupsData || {}).length
      };
    } else if (groupConfig.filterFunction) {
      // For filtered data (wh, or any custom filter)
      const filteredPositions = ordersData.data.positions.filter(groupConfig.filterFunction);
      const filteredPending = ordersData.data.pending.filter(groupConfig.filterFunction);
      return {
        positions: filteredPositions,
        pending: filteredPending,
        groups: {},
        count: filteredPositions.length + filteredPending.length
      };
    } else {
      // For all orders
      return {
        positions: ordersData.data.positions,
        pending: ordersData.data.pending,
        groups: {},
        count: ordersData.data.positions.length + ordersData.data.pending.length
      };
    }
  };

  const renderPositionsTable = (positions: Position[]) => (
    <div className="overflow-x-auto w-full max-w-full">
      <table className="w-full min-w-max text-sm text-white font-mono">
        <thead className="bg-gray-700">
          <tr>
            <th className="px-2 py-2 text-right">Profit</th>
            <th className="px-2 py-2 text-right">Current</th>
            <th className="px-2 py-2 text-right">Entry</th>
            <th className="px-2 py-2 text-right">SL</th>
            <th className="px-2 py-2 text-right">TP</th>
            <th className="px-2 py-2 text-center">RR</th>
            <th className="px-2 py-2 text-left">Type</th>
            <th className="px-2 py-2 text-right">Vol</th>
            <th className="px-2 py-2 text-left">Comment</th>
            <th className="px-2 py-2 text-left">Symbol</th>
            <th className="px-2 py-2 text-left">Ticket</th>
            {settings.orders.showCloseColumn && (
              <th className="px-2 py-2 text-center">Close</th>
            )}
          </tr>
        </thead>
        <tbody>
          {positions.map((pos) => (
            <tr key={pos.ticket} className="border-b border-gray-600 hover:bg-gray-700">
              <td className="px-2 py-2 text-right text-lg text-yellow-400">{formatProfitLoss(pos.profit, 0, settings.currency.showThb, settings.currency.usdToThbRate)}</td>
              <td className="px-2 py-2 text-right">{formatNumberBySymbol(pos.current, pos.symbol)}</td>
              <td className="px-2 py-2 text-right">{formatNumberBySymbol(pos.entry, pos.symbol)}</td>
              <td className="px-2 py-2 text-right">
                {pos.sl ? (
                  <div>
                    <div>{formatNumberBySymbol(pos.sl, pos.symbol)}</div>
                    {formatProfitLoss(calculateProfitLoss(pos.entry, pos.sl, pos.volume, pos.symbol, pos.type), 0, settings.currency.showThb, settings.currency.usdToThbRate)}
                  </div>
                ) : "-"}
              </td>
              <td className="px-2 py-2 text-right">
                {pos.tp ? (
                  <div>
                    <div>{formatNumberBySymbol(pos.tp, pos.symbol)}</div>
                    {formatProfitLoss(calculateProfitLoss(pos.entry, pos.tp, pos.volume, pos.symbol, pos.type), 0, settings.currency.showThb, settings.currency.usdToThbRate)}
                  </div>
                ) : "-"}
              </td>
              <td className="px-2 py-2 text-center text-nowrap">
                {pos.tp ? (
                  <span className="text-yellow-400 text-sm">
                    {pos.magic ?
                      calculateRiskRewardWithMagic(pos.entry, pos.tp, pos.type, pos.symbol, pos.magic) :
                      pos.sl ? calculateRiskReward(pos.entry, pos.sl, pos.tp, pos.type) : "-"
                    }
                  </span>
                ) : "-"}
                <br />
                {pos.tp ? (
                  <>
                    (<span className="text-yellow-400 mx-1 text-xs">
                        {Math.round(Math.abs(pos.entry - pos.tp) / getSymbolPoint(pos.symbol))} Pts
                      </span>)
                  </>
                ) : ""}
              </td>
              <td className="px-2 py-2">
                <span className={pos.type === "BUY" ? "text-green-400" : "text-red-400"}>
                  {pos.type}
                </span>
              </td>
              <td className="px-2 py-2 text-right">{formatNumber(pos.volume)}</td>
              <td className="px-2 py-2 text-xs">{pos.comment}</td>
              <td className="px-2 py-2">{pos.symbol}</td>
              <td className="px-2 py-2">{pos.ticket}</td>
              {settings.orders.showCloseColumn && (
                <td className="px-2 py-2 text-center">
                  <button
                    onClick={() => handleCloseOrder(pos.ticket, 'position')}
                    className="px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700"
                    title="Close Position"
                  >
                    ✕
                  </button>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  const renderPendingTable = (pending: PendingOrder[]) => (
    <div className="overflow-x-auto w-full max-w-full">
      <table className="w-full min-w-max text-sm text-white font-mono">
        <thead className="bg-gray-700">
          <tr>
            <th className="px-2 py-2 text-right">Entry</th>
            <th className="px-2 py-2 text-right">Current</th>
            <th className="px-2 py-2 text-right">SL</th>
            <th className="px-2 py-2 text-right">TP</th>
            <th className="px-2 py-2 text-center">RR</th>
            <th className="px-2 py-2 text-left">Type</th>
            <th className="px-2 py-2 text-right">Vol</th>
            <th className="px-2 py-2 text-left">Comment</th>
            <th className="px-2 py-2 text-left">Symbol</th>
            <th className="px-2 py-2 text-left">Ticket</th>
            {settings.orders.showCloseColumn && (
              <th className="px-2 py-2 text-center">Cancel</th>
            )}
          </tr>
        </thead>
        <tbody>
          {pending.map((order) => (
            <tr key={order.ticket} className="border-b border-gray-600 hover:bg-gray-700">
              
              <td className="px-2 py-2 text-right text-lg text-yellow-400">{formatNumberBySymbol(order.entry, order.symbol)}</td>
              <td className="px-2 py-2 text-right">{formatNumberBySymbol(order.current, order.symbol)}</td>
              <td className="px-2 py-2 text-right">
                {order.sl ? (
                  <div>
                    <div>{formatNumberBySymbol(order.sl, order.symbol)}</div>
                    {formatProfitLoss(calculateProfitLoss(order.entry, order.sl, order.volume, order.symbol, order.type), 0, settings.currency.showThb, settings.currency.usdToThbRate)}
                  </div>
                ) : "-"}
              </td>
              <td className="px-2 py-2 text-right">
                {order.tp ? (
                  <div>
                    <div>{formatNumberBySymbol(order.tp, order.symbol)}</div>
                    {formatProfitLoss(calculateProfitLoss(order.entry, order.tp, order.volume, order.symbol, order.type), 0, settings.currency.showThb, settings.currency.usdToThbRate)}
                  </div>
                ) : "-"}
              </td>
              <td className="px-2 py-2 text-center text-nowrap">
                {order.sl && order.tp ? (
                  <span className="text-yellow-400 text-sm">
                    {calculateRiskReward(order.entry, order.sl, order.tp, order.type)}
                  </span>
                ) : "-"}
                <br />
                {order.tp ? (
                  <>
                    (<span className="text-yellow-400 mx-1 text-xs">
                        {Math.round(Math.abs(order.entry - order.tp) / getSymbolPoint(order.symbol))} Pts
                      </span>)
                  </>
                ) : ""}
              </td>
              <td className="px-2 py-2">
                <span className={order.type.includes("Buy") ? "text-green-400" : "text-red-400"}>
                  {order.type}
                </span>
              </td>
              <td className="px-2 py-2 text-right">{formatNumber(order.volume)}</td>
              <td className="px-2 py-2 text-xs">{order.comment}</td>
              <td className="px-2 py-2">{order.symbol}</td>
              <td className="px-2 py-2">{order.ticket}</td>
              {settings.orders.showCloseColumn && (
                <td className="px-2 py-2 text-center">
                  <button
                    onClick={() => handleCloseOrder(order.ticket, 'pending')}
                    className="px-2 py-1 bg-orange-600 text-white text-xs rounded hover:bg-orange-700"
                    title="Cancel Order"
                  >
                    ✕
                  </button>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  return (
    <div className="space-y-4 w-full max-w-full overflow-hidden">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold text-white">Orders & Positions</h2>
          {lastUpdate && (
            <p className="text-sm text-gray-400">
              Last updated: {lastUpdate.toLocaleTimeString()}
              {/* {isLoading && <span className="ml-2 text-yellow-400">Refreshing...</span>} */}
            </p>
          )}
        </div>
        <div className="flex gap-2 items-center">
          <label className="flex items-center text-sm text-gray-300">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-2"
            />
            Auto
          </label>
          <button
            onClick={fetchOrdersData}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {isLoading ? "Refreshing..." : "Refresh"}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900 border border-red-600 text-red-200 px-4 py-3 rounded">
          <strong>Error:</strong> {error}
        </div>
      )}

      {/* Sub-tabs - Dynamically generated */}
      <div className="flex space-x-2 border-b border-gray-600">
        {GROUP_CONFIGS.map((groupConfig) => {
          const groupData = getGroupData(groupConfig.id);
          const count = groupConfig.requiresGroupId ? groupData.count : groupData.count;

          return (
            <button
              key={groupConfig.id}
              className={`px-4 py-2 text-sm font-medium ${
                activeSubTab === groupConfig.id ? "border-b-2 border-blue-400 text-blue-400" : "text-gray-400"
              }`}
              onClick={() => setActiveSubTab(groupConfig.id)}
            >
              {groupConfig.displayName} ({count})
            </button>
          );
        })}
      </div>

      {/* Content */}
      <div className="bg-gray-800 rounded-lg p-4">
        {!ordersData || !ordersData.data ? (
          <div className="text-center text-gray-400 py-8">
            {isLoading ? "Loading orders data..." : "No data available"}
          </div>
        ) : (
          <>
            {(() => {
              const groupConfig = GROUP_CONFIGS.find(config => config.id === activeSubTab);
              if (!groupConfig) return null;

              const groupData = getGroupData(activeSubTab);

              if (groupConfig.requiresGroupId) {
                // Render grouped data (zd, in, etc.)
                return (
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">{groupConfig.displayName}</h3>
                    {Object.keys(groupData.groups).length === 0 ? (
                      <p className="text-gray-400">No {groupConfig.displayName.toLowerCase()} found</p>
                    ) : (
                      <div className="space-y-4">
                        {Object.entries(groupData.groups).map(([groupKey, group]: [string, any]) => (
                          <div key={groupKey} className="border border-gray-600 rounded-lg p-4">
                            <div className="flex justify-between items-center mb-3">
                              <div>
                                {/* <h4 className="text-lg font-medium text-white">{groupConfig.displayName} Group: {group.group_id}</h4> */}
                                <h4 className="text-lg font-medium text-white">Group: {group.group_id}</h4>
                                <div className="flex items-center space-x-4">
                                  <p className="text-sm text-gray-400">
                                    {group.positions.length} positions, {group.pending.length} pending
                                  </p>
                                  {group.positions.length > 0 && (
                                    <div className="text-sm">
                                      <span className="text-gray-400 mr-2">Profit:</span>
                                      {formatProfitLoss(calculateTotalProfit(group.positions), 1, settings.currency.showThb, settings.currency.usdToThbRate)}
                                    </div>
                                  )}
                                </div>
                              </div>
                              <button
                                onClick={() => handleCloseGroup(activeSubTab.toUpperCase() as "ZD" | "IN", group.group_id)}
                                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                              >
                                X
                              </button>
                            </div>

                            {group.positions.length > 0 && (
                              <div className="mb-4">
                                <h5 className="text-md font-medium text-white mb-2">Positions:</h5>
                                {renderPositionsTable(group.positions)}
                              </div>
                            )}

                            {group.pending.length > 0 && (
                              <div>
                                <h5 className="text-md font-medium text-white mb-2">Pending Orders:</h5>
                                {renderPendingTable(group.pending)}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                );
              } else {
                // Render non-grouped data (orders, wh, etc.)
                if (activeSubTab === "orders") {
                  return (
                    <div className="space-y-6">
                      {/* Open Positions Section */}
                      <div>
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-lg font-semibold text-white">
                            Open Positions ({groupData.positions.length})
                          </h3>
                          {groupData.positions.length > 0 && (
                            <div className="text-sm">
                              <span className="text-gray-400 mr-2">Total Profit:</span>
                              {formatProfitLoss(calculateTotalProfit(groupData.positions), 1, settings.currency.showThb, settings.currency.usdToThbRate)}
                            </div>
                          )}
                        </div>
                        {groupData.positions.length === 0 ? (
                          <p className="text-gray-400">No open positions</p>
                        ) : (
                          renderPositionsTable(groupData.positions)
                        )}
                      </div>

                      {/* Pending Orders Section */}
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-4">
                          Pending Orders ({groupData.pending.length})
                        </h3>
                        {groupData.pending.length === 0 ? (
                          <p className="text-gray-400">No pending orders</p>
                        ) : (
                          renderPendingTable(groupData.pending)
                        )}
                      </div>
                    </div>
                  );
                } else {
                  // For filtered data (wh, etc.)
                  const totalOrders = groupData.positions.length + groupData.pending.length;

                  if (totalOrders === 0) {
                    return <p className="text-gray-400">No {groupConfig.displayName.toLowerCase()} orders found</p>;
                  }

                  return (
                    <div className="space-y-6">
                      <h3 className="text-lg font-semibold text-white mb-4">{groupConfig.displayName} Orders</h3>

                      {/* Filtered Positions Section */}
                      {groupData.positions.length > 0 && (
                        <div>
                          <div className="flex justify-between items-center mb-4">
                            <h4 className="text-md font-semibold text-white">
                              {groupConfig.displayName} Positions ({groupData.positions.length})
                            </h4>
                            <div className="text-sm">
                              <span className="text-gray-400 mr-2">Total Profit:</span>
                              {formatProfitLoss(calculateTotalProfit(groupData.positions), 1, settings.currency.showThb, settings.currency.usdToThbRate)}
                            </div>
                          </div>
                          {renderPositionsTable(groupData.positions)}
                        </div>
                      )}

                      {/* Filtered Pending Orders Section */}
                      {groupData.pending.length > 0 && (
                        <div>
                          <h4 className="text-md font-semibold text-white mb-4">
                            {groupConfig.displayName} Pending Orders ({groupData.pending.length})
                          </h4>
                          {renderPendingTable(groupData.pending)}
                        </div>
                      )}
                    </div>
                  );
                }
              }
            })()}
          </>
        )}
      </div>

      {/* Confirmation Dialog */}
      {showConfirmation && pendingAction && (
        <ConfirmationDialog
          isOpen={showConfirmation}
          title={
            pendingAction.action === 'close_group'
              ? "Confirm Close Group"
              : pendingAction.action === 'close_position'
              ? "Confirm Close Position"
              : "Confirm Cancel Order"
          }
          message={
            pendingAction.action === 'close_group'
              ? `Are you sure you want to close all positions and pending orders for ${pendingAction.group_type} group "${pendingAction.group_id}"?`
              : pendingAction.action === 'close_position'
              ? `Are you sure you want to close position #${pendingAction.ticket}?`
              : `Are you sure you want to cancel order #${pendingAction.ticket}?`
          }
          onConfirm={handleConfirmSubmit}
          onCancel={handleCancelSubmit}
        />
      )}

      {/* Alert Dialog */}
      <AlertDialog
        isOpen={alert.isOpen}
        title={alert.title}
        message={alert.message}
        type={alert.type}
        onClose={closeAlert}
        autoClose={alert.autoClose}
      />
    </div>
  );
}
