// components/TabAIBot.tsx
"use client";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Select from "react-select";
import { SYMBOL_OPTIONS, TIMEFRAME_OPTIONS, AI_PROVIDER_OPTIONS } from "@/config/global";
import { useAlert } from "@/hooks/useAlert";
import AlertDialog from "./AlertDialog";
import ConfirmationDialog from "./ConfirmationDialog";

const WEBHOOK_AI_ANALYSIS = process.env.NEXT_PUBLIC_WEBHOOK_URL_AI_ANALYSIS;

interface AIBotConfig {
  id: string;
  name: string;
  enabled: boolean;
  aiProvider: "gpt" | "gemini";
  webhookUrl: string;
  autoPlaceOrder: boolean;
  scheduleCheck: boolean;
  checkInterval: number; // in minutes
  symbol: string;
  timeframes: string[]; // Multiple timeframes for analysis
  barback: number;
  additionalPrompt: string;
  createdAt: string;
  lastActive?: string;
}



interface AIAnalysisResponse {
  error: boolean;
  message: string;
  symbol: string;
  timeframes: string[];
  ai_provider: string;
  analysis: string;
  custom_prompt: string;
  image_analyzed: boolean;
  use_signal_format: number;
  timestamp: string;
  signal_data?: {
    signal_id: string;
    symbol: string;
    signal_type: string;
    entry_price: string;
    sl_price: string;
    tp1_price: string;
    tp2_price: string;
    tp3_price: string;
    tp4_price?: string;
    tp5_price?: string;
  };
  structured_signal: boolean;
}

interface AnalysisHistoryItem {
  id: string;
  botName: string;
  timestamp: string;
  response: AIAnalysisResponse;
  starred?: boolean;
}

// History Item Component
function HistoryItem({
  item,
  onExtract,
  onEdit,
  onDelete,
  onToggleStarred
}: {
  item: AnalysisHistoryItem;
  onExtract: (analysis: string) => void;
  onEdit: (itemId: string, newAnalysis: string) => void;
  onDelete: (itemId: string) => void;
  onToggleStarred: (itemId: string) => void;
}) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedAnalysis, setEditedAnalysis] = useState(item.response.analysis);

  const handleSaveEdit = () => {
    onEdit(item.id, editedAnalysis);
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditedAnalysis(item.response.analysis);
    setIsEditing(false);
  };

  return (
    <div className="bg-gray-800 p-3 rounded-md">
      <div className="grid grid-cols-1 sm:grid-cols-2 justify-between items-start mb-2">
        <div>
          <p className="text-white font-medium">{item.botName}</p>
          <p className="text-gray-400 text-sm">
            {new Date(item.timestamp).toLocaleString()} • {item.response.symbol} • {item.response.ai_provider}
          </p>
        </div>
        <div className="flex space-x-2">
          {!isEditing && (
            <>
              <button
                onClick={() => onToggleStarred(item.id)}
                className={`px-3 py-1 text-sm rounded ${
                  item.starred
                    ? 'bg-yellow-600 text-white hover:bg-yellow-700'
                    : 'bg-gray-600 text-white hover:bg-gray-700'
                }`}
                title={item.starred ? "Remove from starred" : "Add to starred"}
              >
                {item.starred ? '⭐ Starred' : '☆ Star'}
              </button>
              <button
                onClick={() => onExtract(item.response.analysis)}
                className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                title="Extract to Input Tab"
              >
                📈 Extract
              </button>
              <button
                onClick={() => setIsEditing(true)}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                title="Edit Analysis"
              >
                ✏️ Edit
              </button>
              <button
                onClick={() => onDelete(item.id)}
                className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                title="Delete History Item"
              >
                🗑️ Delete
              </button>
            </>
          )}
          {isEditing && (
            <>
              <button
                onClick={handleSaveEdit}
                className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
              >
                💾 Save
              </button>
              <button
                onClick={handleCancelEdit}
                className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
              >
                ❌ Cancel
              </button>
            </>
          )}
        </div>
      </div>
      <div className="bg-gray-900 p-2 rounded text-xs text-gray-300">
        {isEditing ? (
          <textarea
            value={editedAnalysis}
            onChange={(e) => setEditedAnalysis(e.target.value)}
            className="w-full h-32 bg-gray-800 text-gray-300 p-2 rounded border border-gray-600 XXresize-none text-sm"
            placeholder="Edit analysis..."
          />
        ) : (
          <div className="max-h-20 overflow-y-auto">
            <pre className="whitespace-pre-wrap">{item.response.analysis}</pre>
          </div>
        )}
      </div>
    </div>
  );
}

export default function TabAIBot({
  customStyle,
  sendWebhook,
  loading,
}: {
  customStyle: Object;
  sendWebhook: Function;
  loading: boolean;
}) {
  const pathname = usePathname();
  const [bots, setBots] = useState<AIBotConfig[]>([]);
  const [showAddBot, setShowAddBot] = useState(false);
  const [editingBot, setEditingBot] = useState<string | null>(null);
  const [newBot, setNewBot] = useState<Partial<AIBotConfig>>({
    name: "",
    aiProvider: "gpt",
    webhookUrl: "",
    autoPlaceOrder: false,
    scheduleCheck: false,
    checkInterval: 60,
    symbol: "XAUUSD",
    timeframes: ["M15", "H1", "H4"],
    barback: 50,
    additionalPrompt: "Focus on current market session timing, recent news impact, and provide conservative risk management. Consider if this is a good time to enter based on volatility and market conditions.",
    enabled: false,
  });

  const [selectedBotForAnalysis, setSelectedBotForAnalysis] = useState<string | null>(null);

  // AI analysis state
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [aiResult, setAiResult] = useState<AIAnalysisResponse | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisStartTime, setAnalysisStartTime] = useState<number | null>(null);
  const [analysisElapsedTime, setAnalysisElapsedTime] = useState<string>("00:00:00");
  const [analysisDuration, setAnalysisDuration] = useState<string | null>(null);

  // Popup and history state
  const [showAnalysisPopup, setShowAnalysisPopup] = useState(false);
  const [showConfirmationPopup, setShowConfirmationPopup] = useState(false);
  const [currentBotForAnalysis, setCurrentBotForAnalysis] = useState<AIBotConfig | null>(null);
  const [editablePrompt, setEditablePrompt] = useState<string>("");
  const [analysisHistory, setAnalysisHistory] = useState<AnalysisHistoryItem[]>([]);
  const [showHistory, setShowHistory] = useState(true);
  const [historyPage, setHistoryPage] = useState(1);
  const [historyPerPage] = useState(5);
  const [historyFilter, setHistoryFilter] = useState<'all' | 'starred'>('all');

  // Export/Import state
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [selectedBotsForExport, setSelectedBotsForExport] = useState<string[]>([]);
  const [importFile, setImportFile] = useState<File | null>(null);

  // Predefined prompt templates
  const promptTemplates = [
    {
      name: "Conservative Analysis",
      prompt: "Focus on conservative entry points with strong risk management. Prioritize high-probability setups with clear support/resistance levels."
    },
    {
      name: "Scalping Setup",
      prompt: "Analyze for short-term scalping opportunities. Focus on 5-15 minute entries with tight stops and quick profit targets."
    },
    {
      name: "Swing Trading",
      prompt: "Look for swing trading opportunities with 1-3 day holding periods. Consider daily and 4H timeframe confluence."
    },
    {
      name: "News Impact",
      prompt: "Consider recent fundamental news and economic events. How might upcoming releases affect this pair? Factor in market sentiment."
    },
    {
      name: "Session Analysis",
      prompt: "Analyze based on current trading session (Asian/European/US). Consider session-specific volatility and typical price movements."
    }
  ];

  const { alert, showSuccess, showError, showWarning, closeAlert } = useAlert();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [botToDelete, setBotToDelete] = useState<string | null>(null);

  // Token estimation function
  const estimateTokens = (text: string): number => {
    if (!text) return 0;

    const words = text.split(/\s+/).filter(word => word.length > 0);
    let tokenCount = 0;

    words.forEach(word => {
      const cleanWord = word.replace(/[^\w]/g, '');
      if (cleanWord.length <= 3) {
        tokenCount += 1;
      } else if (cleanWord.length <= 8) {
        tokenCount += 1;
      } else {
        tokenCount += 2;
      }
    });

    // Add punctuation tokens
    const punctuationCount = (text.match(/[.,!?;:()[\]{}"'-]/g) || []).length;
    tokenCount += Math.ceil(punctuationCount / 2);

    // Add system overhead
    tokenCount += 30;

    // Fallback calculation
    if (tokenCount === 30) {
      tokenCount = Math.ceil(text.length / 4);
    }

    return tokenCount;
  };

  // Format number with commas
  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };

  useEffect(() => {
    loadBots();
    loadAnalysisHistory();
  }, []);

  const loadBots = async () => {
    try {
      const response = await fetch('/api/ai-bots', {
        headers: {
          'x-current-path': pathname
        }
      });
      if (response.ok) {
        const data = await response.json();
        setBots(data);
      }
    } catch (error) {
      console.error('Failed to load AI bots:', error);
    }
  };

  const saveBots = async (botsToSave: AIBotConfig[]) => {
    try {
      await fetch('/api/ai-bots', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-current-path': pathname
        },
        body: JSON.stringify(botsToSave),
      });
    } catch (error) {
      console.error('Failed to save AI bots:', error);
    }
  };

  const loadAnalysisHistory = async () => {
    try {
      const response = await fetch('/api/ai-analysis-history', {
        headers: {
          'x-current-path': pathname
        }
      });
      if (response.ok) {
        const data = await response.json();
        setAnalysisHistory(data);
      }
    } catch (error) {
      console.error('Failed to load analysis history:', error);
    }
  };

  const saveAnalysisHistory = async (historyToSave: AnalysisHistoryItem[]) => {
    try {
      await fetch('/api/ai-analysis-history', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(historyToSave),
      });
    } catch (error) {
      console.error('Failed to save analysis history:', error);
    }
  };

  const addBot = () => {
    if (!newBot.name || !newBot.aiProvider || !newBot.symbol || !newBot.timeframes || newBot.timeframes.length === 0) {
      showError('Please fill in all required fields including at least one timeframe');
      return;
    }

    if (editingBot) {
      // Update existing bot
      const updatedBots = bots.map(bot =>
        bot.id === editingBot
          ? {
              ...bot,
              name: newBot.name!,
              aiProvider: newBot.aiProvider as "gpt" | "gemini",
              webhookUrl: newBot.webhookUrl || "",
              autoPlaceOrder: newBot.autoPlaceOrder || false,
              scheduleCheck: newBot.scheduleCheck || false,
              checkInterval: newBot.checkInterval || 60,
              symbol: newBot.symbol || "XAUUSD",
              timeframes: newBot.timeframes || ["M15", "H1", "H4"],
              barback: newBot.barback || 50,
              additionalPrompt: newBot.additionalPrompt || "",
            }
          : bot
      );
      setBots(updatedBots);
      saveBots(updatedBots);
      showSuccess('AI Bot updated successfully!');
    } else {
      // Add new bot
      const botConfig: AIBotConfig = {
        id: Date.now().toString(),
        name: newBot.name,
        enabled: false,
        aiProvider: newBot.aiProvider as "gpt" | "gemini",
        webhookUrl: newBot.webhookUrl || "",
        autoPlaceOrder: newBot.autoPlaceOrder || false,
        scheduleCheck: newBot.scheduleCheck || false,
        checkInterval: newBot.checkInterval || 60,
        symbol: newBot.symbol || "XAUUSD",
        timeframes: newBot.timeframes || ["M15", "H1", "H4"],
        barback: newBot.barback || 50,
        additionalPrompt: newBot.additionalPrompt || "",
        createdAt: new Date().toISOString(),
      };

      const updatedBots = [...bots, botConfig];
      setBots(updatedBots);
      saveBots(updatedBots);
      showSuccess('AI Bot added successfully!');
    }

    resetBotForm();
  };

  const resetBotForm = () => {
    setNewBot({
      name: "",
      aiProvider: "gpt",
      webhookUrl: "",
      autoPlaceOrder: false,
      scheduleCheck: false,
      checkInterval: 60,
      symbol: "XAUUSD",
      timeframes: ["M15", "H1", "H4"],
      barback: 50,
      additionalPrompt: "Focus on current market session timing, recent news impact, and provide conservative risk management. Consider if this is a good time to enter based on volatility and market conditions.",
      enabled: false,
    });
    setShowAddBot(false);
    setEditingBot(null);
  };

  const editBot = (botId: string) => {
    const bot = bots.find(b => b.id === botId);
    if (bot) {
      setNewBot({
        name: bot.name,
        aiProvider: bot.aiProvider,
        webhookUrl: bot.webhookUrl,
        autoPlaceOrder: bot.autoPlaceOrder,
        scheduleCheck: bot.scheduleCheck,
        checkInterval: bot.checkInterval,
        symbol: bot.symbol,
        timeframes: bot.timeframes,
        barback: bot.barback,
        additionalPrompt: bot.additionalPrompt,
        enabled: bot.enabled,
      });
      setEditingBot(botId);
      setShowAddBot(true);
    }
  };

  const deleteBot = (botId: string) => {
    setBotToDelete(botId);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = () => {
    if (botToDelete) {
      const updatedBots = bots.filter(bot => bot.id !== botToDelete);
      setBots(updatedBots);
      saveBots(updatedBots);
      showSuccess('AI Bot deleted successfully!');
    }
    setShowDeleteConfirm(false);
    setBotToDelete(null);
  };

  const toggleBot = (botId: string) => {
    const updatedBots = bots.map(bot =>
      bot.id === botId ? { ...bot, enabled: !bot.enabled } : bot
    );
    setBots(updatedBots);
    saveBots(updatedBots);
  };



  const analyzeWithAI = async (botId: string) => {
    const bot = bots.find(b => b.id === botId);
    if (!bot) {
      showError('Bot not found');
      return;
    }

    if (!WEBHOOK_AI_ANALYSIS) {
      showError('AI Analysis webhook URL not configured');
      return;
    }

    // Show confirmation popup instead of directly analyzing
    setCurrentBotForAnalysis(bot);
    setEditablePrompt(bot.additionalPrompt || "Provide detailed technical analysis with clear entry and exit points.");
    setShowConfirmationPopup(true);
  };

  // Timer utility function
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Timer effect for analysis
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isAnalyzing && analysisStartTime) {
      interval = setInterval(() => {
        const elapsed = Math.floor((Date.now() - analysisStartTime) / 1000);
        setAnalysisElapsedTime(formatTime(elapsed));
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isAnalyzing, analysisStartTime]);

  const performAnalysis = async () => {
    if (!currentBotForAnalysis) return;

    try {
      const startTime = Date.now();
      setIsAnalyzing(true);
      setAnalysisStartTime(startTime);
      setAnalysisElapsedTime("00:00:00");
      setAnalysisDuration(null);
      setSelectedBotForAnalysis(currentBotForAnalysis.id);

      // Prepare the analysis request for Python webhook using the editable prompt
      const analysisRequest = {
        symbol: currentBotForAnalysis.symbol,
        timeframes: currentBotForAnalysis.timeframes,
        barback: currentBotForAnalysis.barback,
        prompt: editablePrompt,
        ai: currentBotForAnalysis.aiProvider,
        image: uploadedImage ? await fileToBase64(uploadedImage) : "",
        use_signal_format: true
      };

      console.log('Sending analysis request to API route');
      console.log('Request payload:', analysisRequest);

      const response = await fetch('/api/ai-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(analysisRequest),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API response error:', response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const apiResponse = await response.json();

      // Check if the API proxy was successful
      if (!apiResponse.success) {
        const error = apiResponse.error;
        if (error?.type === 'webhook_internal_error') {
          throw new Error(`Python webhook error (500): ${error.details || 'Internal server error'}. Check your Python server logs.`);
        } else {
          throw new Error(`Webhook error (${apiResponse.status}): ${error?.message || apiResponse.data?.message || 'Unknown error'}`);
        }
      }

      const result: AIAnalysisResponse = apiResponse.data;

      if (result.error) {
        showError(`AI Analysis error: ${result.message}`);
        return;
      }

      // Save to history
      const historyItem: AnalysisHistoryItem = {
        id: Date.now().toString(),
        botName: currentBotForAnalysis.name,
        timestamp: result.timestamp,
        response: result
      };

      const updatedHistory = [historyItem, ...analysisHistory];
      setAnalysisHistory(updatedHistory);
      saveAnalysisHistory(updatedHistory);

      setAiResult(result);
      setShowConfirmationPopup(false);
      setShowAnalysisPopup(true);

      if (currentBotForAnalysis.autoPlaceOrder && result.structured_signal) {
        // Auto place order using the analysis text with lot distribution
        await autoPlaceOrderWithDistribution(result.analysis);
        showSuccess('AI analysis completed and order placed automatically!');
      } else {
        showSuccess('AI analysis completed! Click to view results.');
      }

    } catch (error) {
      console.error('Failed to analyze with AI:', error);

      // Provide specific error messages based on error type
      if (error instanceof TypeError && error.message.includes('fetch')) {
        showError(`Network error: Cannot connect to ${WEBHOOK_AI_ANALYSIS}. Please check if your Python webhook is running and accessible.`);
      } else if (error instanceof Error) {
        if (error.message.includes('CORS')) {
          showError('CORS error: Your Python webhook needs to allow cross-origin requests. Add CORS headers to your Flask/FastAPI app.');
        } else if (error.message.includes('HTTP error')) {
          showError(`Webhook error: ${error.message}`);
        } else {
          showError(`Analysis failed: ${error.message}`);
        }
      } else {
        showError('Failed to analyze with AI. Check console for details.');
      }
    } finally {
      if (analysisStartTime) {
        const endTime = Date.now();
        const duration = Math.floor((endTime - analysisStartTime) / 1000);
        setAnalysisDuration(formatTime(duration));
      }
      setIsAnalyzing(false);
      setAnalysisStartTime(null);
    }
  };

  // Copy functionality
  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showSuccess(`${type} copied to clipboard!`);
    } catch (error) {
      console.error('Failed to copy:', error);
      showError('Failed to copy to clipboard');
    }
  };

  const copyPrompt = () => {
    if (aiResult?.custom_prompt) {
      copyToClipboard(aiResult.custom_prompt, 'Prompt');
    } else if (editablePrompt) {
      copyToClipboard(editablePrompt, 'Prompt');
    }
  };

  const copyAnalysis = () => {
    if (aiResult?.analysis) {
      copyToClipboard(aiResult.analysis, 'Analysis');
    }
  };

  // History management functions
  const deleteHistoryItem = (itemId: string) => {
    const updatedHistory = analysisHistory.filter(item => item.id !== itemId);
    setAnalysisHistory(updatedHistory);
    saveAnalysisHistory(updatedHistory);
    showSuccess('History item deleted!');
  };

  const editHistoryItem = (itemId: string, newAnalysis: string) => {
    const updatedHistory = analysisHistory.map(item =>
      item.id === itemId
        ? { ...item, response: { ...item.response, analysis: newAnalysis } }
        : item
    );
    setAnalysisHistory(updatedHistory);
    saveAnalysisHistory(updatedHistory);
    showSuccess('History item updated!');
  };

  const toggleStarredHistoryItem = (itemId: string) => {
    const updatedHistory = analysisHistory.map(item =>
      item.id === itemId
        ? { ...item, starred: !item.starred }
        : item
    );
    setAnalysisHistory(updatedHistory);
    saveAnalysisHistory(updatedHistory);
    const item = updatedHistory.find(h => h.id === itemId);
    showSuccess(item?.starred ? 'Added to starred!' : 'Removed from starred!');
  };

  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  const extractAndSendAnalysis = async (analysisText: string) => {
    try {
      // Parse the AI result to extract trading signal
      const parsedResult = parseLongText(analysisText, aiResult?.symbol || 'UNKNOWN');

      if (parsedResult) {
        await sendWebhook('g_input', parsedResult);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to extract and send analysis:', error);
      return false;
    }
  };

  const sendResultToWebhook = async () => {
    if (!aiResult) {
      showError('No AI result to send');
      return;
    }

    try {
      const success = await extractAndSendAnalysis(aiResult.analysis);
      if (success) {
        showSuccess('AI result sent to webhook successfully!');
      } else {
        showError('Could not parse trading signal from AI result');
      }
    } catch (error) {
      console.error('Failed to send result to webhook:', error);
      showError('Failed to send result to webhook');
    }
  };
  
  // Export/Import functions
  const handleExportBots = () => {
    if (selectedBotsForExport.length === 0) {
      showError('Please select at least one bot to export');
      return;
    }

    const botsToExport = bots.filter(bot => selectedBotsForExport.includes(bot.id));
    const exportData = {
      version: "1.0",
      exportDate: new Date().toISOString(),
      bots: botsToExport
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `ai-bots-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    setShowExportDialog(false);
    setSelectedBotsForExport([]);
    showSuccess(`Successfully exported ${botsToExport.length} bot(s)`);
  };

  const handleImportBots = async () => {
    if (!importFile) {
      showError('Please select a file to import');
      return;
    }

    try {
      const fileContent = await importFile.text();
      const importData = JSON.parse(fileContent);

      if (!importData.bots || !Array.isArray(importData.bots)) {
        showError('Invalid file format. Expected bots array.');
        return;
      }

      const importedBots = importData.bots.map((bot: any) => ({
        ...bot,
        id: `imported-${Date.now()}-${Math.random().toString(36).substr(2, 9)}` // Generate new IDs to avoid conflicts
      }));

      const updatedBots = [...bots, ...importedBots];
      setBots(updatedBots);
      saveBots(updatedBots);

      setShowImportDialog(false);
      setImportFile(null);
      showSuccess(`Successfully imported ${importedBots.length} bot(s)`);
    } catch (error) {
      console.error('Import error:', error);
      showError('Failed to import bots. Please check the file format.');
    }
  };

  const toggleBotSelection = (botId: string) => {
    setSelectedBotsForExport(prev =>
      prev.includes(botId)
        ? prev.filter(id => id !== botId)
        : [...prev, botId]
    );
  };

  const selectAllBots = () => {
    setSelectedBotsForExport(bots.map(bot => bot.id));
  };

  const deselectAllBots = () => {
    setSelectedBotsForExport([]);
  };

  const extractToInputTab = (analysisText: string) => {
    console.log('Extracting to Input tab:', analysisText.substring(0, 100) + '...');

    // Switch to Input tab
    const switchTabEvent = new CustomEvent('switchToInputTab');
    window.dispatchEvent(switchTabEvent);
    console.log('Dispatched switchToInputTab event');
    
    setTimeout(() => {
      // Send the analysis text to the input tab's long text box
      const setTextEvent = new CustomEvent('setLongText', {
        detail: { text: analysisText }
      });
      window.dispatchEvent(setTextEvent);
      console.log('Dispatched setLongText event');
    }, 100);

    // Distribute lots after a longer delay to ensure parsing is complete
    setTimeout(() => {
      const distributeEvent = new CustomEvent('distributeLots', {
        detail: { maxLot: 0.1 }
      });
      window.dispatchEvent(distributeEvent);
      console.log('Dispatched distributeLots event');
    }, 300);


    showSuccess('Analysis extracted to Input tab and lots distributed!');
  };

  const autoPlaceOrderWithDistribution = async (analysisText: string) => {
    try {
      // Parse the AI result to extract trading signal
      const parsedResult = parseLongText(analysisText, aiResult?.symbol || 'UNKNOWN');

      if (parsedResult) {
        // Trigger lot distribution and send order like in Input tab
        const distributionEvent = new CustomEvent('distributeLots', {
          detail: {
            parsedData: parsedResult,
            defaultLot: 0.1,
            autoSend: true
          }
        });
        window.dispatchEvent(distributionEvent);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to auto place order with distribution:', error);
      return false;
    }
  };

  // Simplified parseLongText function (same logic as Discord bot)
  const parseLongText = (content: string, symbol: string): any | null => {
    const lines = content.split("\n").map((line) => line.trim());
    const rows: { tp: number; lot: number }[] = [];
    let data = {
      a: "Buy Limit",
      p: 0.0,
      sl: 0.0,
      s: symbol,
      c: "ZD_AI_BOT",
      reason: "",
      risk: "",
    };

    let hasValidData = false;

    lines.forEach((line) => {
      if (line.toLowerCase().startsWith("tp")) {
        const match = line.match(/TP\d\s*:\s*(\d+(\.\d+)?)/i);
        if (match) {
          rows.push({ tp: parseFloat(match[1]), lot: 0.01 });
          hasValidData = true;
        }
      }

      const slMatch = line.match(/SL\s*:\s*(\d+(\.\d+)?)/i);
      if (slMatch) {
        data.sl = parseFloat(slMatch[1]);
        hasValidData = true;
      }

      const symbolMatch = line.match(/Symbol\s*:\s*(\w+)/i);
      if (symbolMatch) {
        data.s = symbolMatch[1].toUpperCase();
        hasValidData = true;
      }

      const signalMatch = line.match(/Signal\s*:\s*(.+)/i);
      if (signalMatch) {
        data.a = signalMatch[1].trim();
        hasValidData = true;
      }

      const priceMatch = line.match(/Price\s*:\s*(\d+(\.\d+)?)([-–](\d+(\.\d+)?))?/i);
      if (priceMatch) {
        const start = parseFloat(priceMatch[1]);
        const end = parseFloat(priceMatch[4]);
        // const mid = (start + end) / 2;
        if (!isNaN(start) && !isNaN(end)) {
          data.p = parseFloat(((start + end) / 2).toFixed(2));
        }else if(!isNaN(start)){
          data.p = parseFloat(start.toFixed(2));
        }
        // data.p = parseFloat(mid.toFixed(2));
        hasValidData = true;
      }

      const reasonMatch = line.match(/Reason\s*:\s*(.+)/i);
      if (reasonMatch) {
        data.reason = reasonMatch[1].trim();
        hasValidData = true;
      }

      const riskMatch = line.match(/Risk\s*:\s*(.+)/i);
      if (riskMatch) {
        data.risk = riskMatch[1].trim();
        hasValidData = true;
      }
    });

    if (hasValidData) {
      return {
        rows: rows.length > 0 ? rows.slice(0, 10) : [{ tp: 0.0, lot: 0.01 }],
        ...data
      };
    }

    return null;
  };

  return (
    <div className="space-y-6 w-full max-w-full overflow-hidden">
      <AlertDialog
        isOpen={alert.isOpen}
        title={alert.title}
        message={alert.message}
        type={alert.type}
        onClose={closeAlert}
      />
      
      <ConfirmationDialog
        isOpen={showDeleteConfirm}
        title="Delete AI Bot"
        message="Are you sure you want to delete this AI bot? This action cannot be undone."
        onConfirm={confirmDelete}
        onCancel={() => setShowDeleteConfirm(false)}
      />
 

      {/* AI Bots Management */}
      <div className="bg-gray-800 p-4 rounded-lg">
        <div className="flex justify-between items-center mb-4">
          {/* <h3 className="text-lg font-semibold text-white">AI Bots</h3> */}
          <div className="flex space-x-2">
            <button
              onClick={() => setShowExportDialog(true)}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              📤 Export
            </button>
            <button
              onClick={() => setShowImportDialog(true)}
              className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700"
            >
              📥 Import
            </button>
            <button
              onClick={() => setShowAddBot(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Add Bot
            </button>
            <button
              onClick={() => setShowHistory(!showHistory)}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              {showHistory ? 'Hide History' : 'View History'}
            </button>
          </div>
        </div>

        {/* Add/Edit Bot Form */}
        {showAddBot && (
          <div className="mb-6 p-4 bg-gray-700 rounded-lg">
            <h4 className="text-md font-semibold text-white mb-3">
              {editingBot ? 'Edit AI Bot' : 'Add New AI Bot'}
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-200 mb-1">Bot Name</label>
                <input
                  type="text"
                  value={newBot.name || ""}
                  onChange={(e) => setNewBot(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-600 text-white rounded-md border border-gray-500"
                  placeholder="Enter bot name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-200 mb-1">AI Provider</label>
                <Select
                  className="text-black"
                  value={AI_PROVIDER_OPTIONS.find(opt => opt.value === newBot.aiProvider)}
                  onChange={(e) => setNewBot(prev => ({ ...prev, aiProvider: e?.value as "gpt" | "gemini" }))}
                  options={AI_PROVIDER_OPTIONS}
                  styles={customStyle}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-200 mb-1">Symbol</label>
                <Select
                  className="text-black"
                  value={SYMBOL_OPTIONS.find(opt => opt.value === newBot.symbol)}
                  onChange={(e) => setNewBot(prev => ({ ...prev, symbol: e?.value || "XAUUSD" }))}
                  options={SYMBOL_OPTIONS}
                  isSearchable
                  styles={customStyle}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-200 mb-1">Timeframes (Multiple)</label>
                <Select
                  className="text-black"
                  isMulti
                  value={TIMEFRAME_OPTIONS.filter(opt => newBot.timeframes?.includes(opt.value))}
                  onChange={(selectedOptions) => {
                    const timeframes = selectedOptions ? selectedOptions.map(opt => opt.value) : [];
                    setNewBot(prev => ({ ...prev, timeframes }));
                  }}
                  options={TIMEFRAME_OPTIONS}
                  styles={customStyle}
                  placeholder="Select multiple timeframes..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-200 mb-1">Bars Back</label>
                <input
                  type="number"
                  value={newBot.barback || 50}
                  onChange={(e) => setNewBot(prev => ({ ...prev, barback: parseInt(e.target.value) || 50 }))}
                  className="w-full px-3 py-2 bg-gray-600 text-white rounded-md border border-gray-500"
                  min="1"
                  max="1000"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-200 mb-1">Check Interval (minutes)</label>
                <input
                  type="number"
                  value={newBot.checkInterval || 60}
                  onChange={(e) => setNewBot(prev => ({ ...prev, checkInterval: parseInt(e.target.value) || 60 }))}
                  className="w-full px-3 py-2 bg-gray-600 text-white rounded-md border border-gray-500"
                  min="1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-200 mb-1">Webhook URL (Optional)</label>
                <input
                  type="text"
                  value={newBot.webhookUrl || ""}
                  onChange={(e) => setNewBot(prev => ({ ...prev, webhookUrl: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-600 text-white rounded-md border border-gray-500"
                  placeholder="Custom webhook URL"
                />
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-200 mb-2">Additional Prompt Template</label>
              <div className="mb-2">
                <Select
                  className="text-black"
                  placeholder="Select a prompt template..."
                  onChange={(option) => {
                    if (option) {
                      setNewBot(prev => ({ ...prev, additionalPrompt: option.prompt }));
                    }
                  }}
                  options={promptTemplates.map(template => ({
                    value: template.name,
                    label: template.name,
                    prompt: template.prompt
                  }))}
                  styles={customStyle}
                  isClearable
                />
              </div>
              <textarea
                value={newBot.additionalPrompt || ""}
                onChange={(e) => setNewBot(prev => ({ ...prev, additionalPrompt: e.target.value }))}
                placeholder="Enter additional instructions for AI analysis..."
                className="w-full px-3 py-2 bg-gray-600 text-white rounded-md border border-gray-500 h-24 XXresize-none"
              />
            </div>
            
            <div className="flex items-center space-x-4 mt-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={newBot.autoPlaceOrder || false}
                  onChange={(e) => setNewBot(prev => ({ ...prev, autoPlaceOrder: e.target.checked }))}
                  className="mr-2"
                />
                <span className="text-gray-200">Auto Place Order</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={newBot.scheduleCheck || false}
                  onChange={(e) => setNewBot(prev => ({ ...prev, scheduleCheck: e.target.checked }))}
                  className="mr-2"
                />
                <span className="text-gray-200">Schedule Check</span>
              </label>
            </div>
            
            <div className="flex space-x-2 mt-4">
              <button
                onClick={addBot}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                {editingBot ? 'Update Bot' : 'Add Bot'}
              </button>
              <button
                onClick={resetBotForm}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {/* Bots List */}
        <div className="space-y-3">
          {bots.map((bot) => (
            <div key={bot.id} className="bg-gray-700 p-4 rounded-lg">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className=" items-center space-x-3">
                    <h4 className="text-white font-medium">{bot.name}</h4>
                    {/* <span className={`px-2 py-1 text-xs rounded ${
                      bot.enabled ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300'
                    }`}>
                      {bot.enabled ? 'Active' : 'Inactive'}
                    </span> */}
                    {/* <span className="px-2 py-1 text-xs rounded bg-blue-600 text-white">
                      {bot.aiProvider.toUpperCase()}
                    </span> */}
                    <span className="px-2 py-1 text-xs rounded bg-purple-600 text-white">
                      {bot.aiProvider.toUpperCase()} • {bot.symbol} • {bot.timeframes.join(', ')} • {bot.barback}
                    </span>
                  </div>

                  <div className="mt-2 text-sm text-gray-300 space-y-1">
                    <p className="text-xs">Auto: {bot.autoPlaceOrder ? 'Yes' : 'No'}, Schedule: {bot.scheduleCheck ? `Every ${bot.checkInterval}min` : 'No'}</p>
                    {bot.lastActive && (
                      <p>Last Active: {new Date(bot.lastActive).toLocaleString()}</p>
                    )}
                    {bot.additionalPrompt && (
                      <div className="mt-2">
                        {/* <p className="text-gray-400 text-xs">Additional Prompt:</p> */}
                        <p className="text-gray-300 text-xs bg-gray-800 p-2 rounded max-h-16 overflow-y-auto">
                          {bot.additionalPrompt}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex flex-col space-y-2 pl-3">
                  <div className="flex space-x-2 justify-end">
                    <button
                      onClick={() => analyzeWithAI(bot.id)}
                      disabled={isAnalyzing}
                      className="w-full px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700 disabled:opacity-50"
                    >
                      {isAnalyzing && selectedBotForAnalysis === bot.id ? 'Analyzing...' : 'Analyze'}
                    </button>
                  </div>
                  <div className="flex space-x-2 mb-5 justify-end">
                    <button
                      onClick={() => editBot(bot.id)}
                      className="w-full px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                    >
                      Edit
                    </button>
                    {/* <button
                      onClick={() => toggleBot(bot.id)}
                      className={`px-3 py-1 text-sm rounded ${
                        bot.enabled
                          ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                          : 'bg-green-600 hover:bg-green-700 text-white'
                      }`}
                    >
                      {bot.enabled ? 'Disable' : 'Enable'}
                    </button> */}
                  </div>
                  <div className="flex space-x-2 mt-5 justify-end">
                    <button
                      onClick={() => deleteBot(bot.id)}
                      className="w-full px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {bots.length === 0 && (
            <div className="text-center py-8 text-gray-400">
              No AI bots configured. Click "Add AI Bot" to get started.
            </div>
          )}
        </div>

        {/* Analysis History */}
        {showHistory && (
          <div className="mt-6 bg-gray-700 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-4">
              <h4 className="text-lg font-semibold text-white">Analysis History</h4>

              {/* Filter Buttons */}
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    setHistoryFilter('all');
                    setHistoryPage(1);
                  }}
                  className={`px-3 py-1 text-sm rounded ${
                    historyFilter === 'all'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                  }`}
                >
                  All ({analysisHistory.length})
                </button>
                <button
                  onClick={() => {
                    setHistoryFilter('starred');
                    setHistoryPage(1);
                  }}
                  className={`px-3 py-1 text-sm rounded ${
                    historyFilter === 'starred'
                      ? 'bg-yellow-600 text-white'
                      : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                  }`}
                >
                  ⭐ Starred ({analysisHistory.filter(item => item.starred).length})
                </button>
              </div>
            </div>

            {(() => {
              const filteredHistory = historyFilter === 'starred'
                ? analysisHistory.filter(item => item.starred)
                : analysisHistory;

              return filteredHistory.length === 0 ? (
                <p className="text-gray-400 text-center py-4">
                  {historyFilter === 'starred' ? 'No starred items yet.' : 'No analysis history yet.'}
                </p>
              ) : (
                <>
                  <div className="space-y-3">
                    {filteredHistory
                      .slice((historyPage - 1) * historyPerPage, historyPage * historyPerPage)
                      .map((item) => (
                        <HistoryItem
                          key={item.id}
                          item={item}
                          onExtract={(analysis) => {
                            extractToInputTab(analysis);
                            setShowHistory(false);
                          }}
                          onEdit={(itemId, newAnalysis) => editHistoryItem(itemId, newAnalysis)}
                          onDelete={(itemId) => deleteHistoryItem(itemId)}
                          onToggleStarred={(itemId) => toggleStarredHistoryItem(itemId)}
                        />
                      ))}
                  </div>

                  {/* Pagination */}
                  {filteredHistory.length > historyPerPage && (
                    <div className="flex justify-center items-center space-x-2 mt-4">
                      <button
                        onClick={() => setHistoryPage(Math.max(1, historyPage - 1))}
                        disabled={historyPage === 1}
                        className="px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50"
                      >
                        Previous
                      </button>
                      <span className="text-gray-300">
                        Page {historyPage} of {Math.ceil(filteredHistory.length / historyPerPage)}
                      </span>
                      <button
                        onClick={() => setHistoryPage(Math.min(Math.ceil(filteredHistory.length / historyPerPage), historyPage + 1))}
                        disabled={historyPage >= Math.ceil(filteredHistory.length / historyPerPage)}
                        className="px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50"
                      >
                        Next
                      </button>
                    </div>
                  )}
                </>
              );
            })()}
          </div>
        )}
      </div>

      {/* AI Analysis Confirmation Popup */}
      {showConfirmationPopup && currentBotForAnalysis && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
            {/* Header */}
            <div className="bg-gray-700 px-6 py-4 border-b border-gray-600">
              <h3 className="text-xl font-semibold text-white flex items-center">
                🤖 AI Analysis [{currentBotForAnalysis.aiProvider.toUpperCase()}]
              </h3>
            </div>

            {/* Info Bar */}
            <div className="bg-gray-750 px-6 py-3 border-b border-gray-600">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-6">
                  <span className="text-gray-300">
                    <span className="text-white">{currentBotForAnalysis.symbol}</span>
                  </span>
                  <span className="text-gray-300">
                    <strong>TF:</strong> <span className="text-white">{currentBotForAnalysis.timeframes.join(', ')}</span>
                  </span>
                  {/* <span className="text-gray-300">
                    <strong>AI Provider:</strong> <span className="text-white">{currentBotForAnalysis.aiProvider.toUpperCase()}</span>
                  </span> */}
                </div>
                {/* <div className="text-gray-300">
                  <strong>Estimated Tokens:</strong>
                  <span className="text-white ml-1">
                    Prompt ~{formatNumber(estimateTokens(editablePrompt))}
                    |
                    Response ~1,000 |
                    Total ~{formatNumber(estimateTokens(editablePrompt) + 1000)}
                  </span>
                </div> */}
              </div>
            </div>

            {/* Single Column Layout - Editable Prompt */}
            <div className="h-[60vh] p-6">
              <h4 className="text-lg font-medium text-white mb-3 flex items-center">
                📝 Custom AI Prompt <span className="text-sm text-gray-400 ml-2">(Editable)</span>
              </h4>
              <textarea
                value={editablePrompt}
                onChange={(e) => setEditablePrompt(e.target.value)}
                placeholder="Enter your custom prompt for AI analysis..."
                className="w-full h-full bg-gray-900 text-gray-300 p-4 rounded border border-gray-600 XXresize-none text-sm"
              />
            </div>

            {/* Button Layout */}
            <div className="bg-gray-700 px-6 py-4 border-t border-gray-600">
              <div className="flex justify-between">
                <button
                  onClick={() => {
                    setShowConfirmationPopup(false);
                    setCurrentBotForAnalysis(null);
                    setEditablePrompt("");
                    setAiResult(null);
                  }}
                  className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                >
                  Close
                </button>

                <div className="flex items-center space-x-3">
                  {isAnalyzing && (
                    <div className="text-white text-sm text-right text-nowrap">
                      ⏱️<span className="font-mono text-yellow-400">{analysisElapsedTime}</span>
                    </div>
                  )}
                  {(
                    <button
                      onClick={performAnalysis}
                      disabled={isAnalyzing}
                      className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
                    >
                      {isAnalyzing ? '🔄 Analyzing...' : '🤖 Analyze Now'}
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Analysis Result Popup */}
      {showAnalysisPopup && aiResult && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg max-w-4xl max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h3 className="text-xl font-semibold text-white">AI Analysis Result</h3>
                {analysisDuration && (
                  <div className="text-sm text-gray-400 mt-1">
                    ⏱️ Analysis completed in: <span className="font-mono text-green-400">{analysisDuration}</span>
                  </div>
                )}
              </div>
              <button
                onClick={() => setShowAnalysisPopup(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="bg-gray-700 p-3 rounded">
                  <p className="text-gray-400">Symbol:</p>
                  <p className="text-white font-medium">{aiResult.symbol}</p>
                </div>
                <div className="bg-gray-700 p-3 rounded">
                  <p className="text-gray-400">AI Provider:</p>
                  <p className="text-white font-medium">{aiResult.ai_provider}</p>
                </div>
                <div className="bg-gray-700 p-3 rounded">
                  <p className="text-gray-400">Timeframes:</p>
                  <p className="text-white font-medium">{aiResult.timeframes.join(', ')}</p>
                </div>
                <div className="bg-gray-700 p-3 rounded">
                  <p className="text-gray-400">Timestamp:</p>
                  <p className="text-white font-medium">{new Date(aiResult.timestamp).toLocaleString()}</p>
                </div>
              </div>

              <div className="bg-gray-700 p-4 rounded">
                <h4 className="text-white font-medium mb-2">Analysis:</h4>
                <div className="bg-gray-900 p-3 rounded max-h-60 overflow-y-auto">
                  <pre className="text-gray-300 text-sm whitespace-pre-wrap">{aiResult.analysis}</pre>
                </div>
              </div>

              {aiResult.signal_data && (
                <div className="bg-gray-700 p-4 rounded">
                  <h4 className="text-white font-medium mb-2">Signal Data:</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <p><span className="text-gray-400">Signal:</span> <span className="text-white">{aiResult.signal_data.signal_type}</span></p>
                    <p><span className="text-gray-400">Entry:</span> <span className="text-white">{aiResult.signal_data.entry_price}</span></p>
                    <p><span className="text-gray-400">SL:</span> <span className="text-white">{aiResult.signal_data.sl_price}</span></p>
                    <p><span className="text-gray-400">TP1:</span> <span className="text-white">{aiResult.signal_data.tp1_price}</span></p>
                    {aiResult.signal_data.tp2_price && (
                      <p><span className="text-gray-400">TP2:</span> <span className="text-white">{aiResult.signal_data.tp2_price}</span></p>
                    )}
                    {aiResult.signal_data.tp3_price && (
                      <p><span className="text-gray-400">TP3:</span> <span className="text-white">{aiResult.signal_data.tp3_price}</span></p>
                    )}
                  </div>
                </div>
              )}

              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    extractToInputTab(aiResult.analysis);
                    setShowAnalysisPopup(false);
                    setAiResult(null);
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Extract to Input Tab
                </button>
                {/* <button
                  onClick={sendResultToWebhook}
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                >
                  Send to Webhook
                </button> */}
                <button
                  onClick={() => setShowAnalysisPopup(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Export Dialog */}
      {showExportDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="bg-gray-700 px-6 py-4 border-b border-gray-600">
              <h3 className="text-xl font-semibold text-white">📤 Export AI Bots</h3>
            </div>

            <div className="p-6 max-h-96 overflow-y-auto">
              <div className="mb-4">
                <div className="flex justify-between items-center mb-3">
                  <p className="text-gray-300">Select bots to export:</p>
                  <div className="flex space-x-2">
                    <button
                      onClick={selectAllBots}
                      className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                    >
                      Select All
                    </button>
                    <button
                      onClick={deselectAllBots}
                      className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
                    >
                      Deselect All
                    </button>
                  </div>
                </div>

                <div className="space-y-2">
                  {bots.map((bot) => (
                    <div key={bot.id} className="flex items-center space-x-3 p-3 bg-gray-700 rounded">
                      <input
                        type="checkbox"
                        checked={selectedBotsForExport.includes(bot.id)}
                        onChange={() => toggleBotSelection(bot.id)}
                        className="w-4 h-4 text-blue-600 bg-gray-600 border-gray-500 rounded"
                      />
                      <div className="flex-1">
                        <p className="text-white font-medium">{bot.name}</p>
                        <p className="text-gray-400 text-sm">
                          {bot.symbol} • {bot.timeframes.join(', ')} • {bot.aiProvider.toUpperCase()}
                        </p>
                      </div>
                      <span className={`px-2 py-1 rounded text-xs ${bot.enabled ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
                        {bot.enabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                  ))}
                </div>

                {bots.length === 0 && (
                  <p className="text-gray-400 text-center py-4">No bots available to export</p>
                )}
              </div>
            </div>

            <div className="bg-gray-700 px-6 py-4 border-t border-gray-600 flex justify-between">
              <button
                onClick={() => {
                  setShowExportDialog(false);
                  setSelectedBotsForExport([]);
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleExportBots}
                disabled={selectedBotsForExport.length === 0}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                Export {selectedBotsForExport.length} Bot(s)
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Import Dialog */}
      {showImportDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg max-w-lg w-full mx-4">
            <div className="bg-gray-700 px-6 py-4 border-b border-gray-600">
              <h3 className="text-xl font-semibold text-white">📥 Import AI Bots</h3>
            </div>

            <div className="p-6">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-200 mb-2">
                  Select JSON file to import:
                </label>
                <input
                  type="file"
                  accept=".json"
                  onChange={(e) => setImportFile(e.target.files?.[0] || null)}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-md border border-gray-600"
                />
                {importFile && (
                  <p className="text-green-400 text-sm mt-2">✅ File selected: {importFile.name}</p>
                )}
              </div>

              <div className="bg-gray-700 p-3 rounded mb-4">
                <p className="text-gray-300 text-sm">
                  <strong>Note:</strong> Imported bots will be added to your existing bots.
                  Duplicate names are allowed and new IDs will be generated.
                </p>
              </div>
            </div>

            <div className="bg-gray-700 px-6 py-4 border-t border-gray-600 flex justify-between">
              <button
                onClick={() => {
                  setShowImportDialog(false);
                  setImportFile(null);
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleImportBots}
                disabled={!importFile}
                className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:opacity-50"
              >
                Import Bots
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
