// components/TabInput.tsx
"use client";
import { useState, useEffect, useRef } from "react";
import Select from "react-select";
import { SYMBOL_OPTIONS, LOT_DISTRIBUTION_CONFIG, mapActionToValid, getSymbolPoint, calculateRiskReward, formatNumberBySymbol } from "@/config/global";
import { formatProfitLoss } from "@/utils/formatters";
import { useAppSettings } from '@/contexts/AppSettingsContext';
import { useConfirmation } from "@/contexts/ConfirmationContext";
import ConfirmationDialog from "./ConfirmationDialog";
import AlertDialog from "./AlertDialog";
import FloatingTextareaButtons from "./FloatingTextareaButtons";
import { getOrGenerateSignalId, extractSignalId } from "@/utils/signalId";
import { useAlert } from "@/hooks/useAlert";

const WEBHOOK_GROUP_2 = process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP2;

type Row = { vtp: number; lot: number };

export default function TabInput({
  customStyle,
  sendWebhook,
  loading,
}: {
  customStyle: Object,
  sendWebhook: Function;
  loading: boolean;
}) {
  const [rows, setRows] = useState<Row[]>([{ vtp: 0.0, lot: 0.01 }]);
  const [text, setText] = useState("");
  const [priceChoices, setPriceChoices] = useState<string[]>([]);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [maxLotPerMessage, setMaxLotPerMessage] = useState(0.1);
  // const [symbolPoint, setSymbolPoint] = useState(0.01);
  const [lotConfig, setLotConfig] = useState(LOT_DISTRIBUTION_CONFIG);
  const [data, setData] = useState({
    a: "Buy Limit",
    p: 0.0,
    vsl: 0.0,
    s: "XAUUSD",
    c: "ZD_INPUT",
    id: "",
    reason: "",
    risk: "",
  });

  // Get confirmation setting from context
  const { requireConfirmation } = useConfirmation();

  // Get app settings for currency display
  const { settings } = useAppSettings();

  // Alert hook for beautiful dialogs
  const { alert, showSuccess, showError, closeAlert } = useAlert();

  // Confirmation dialog state
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Load lot configuration and generate initial Signal ID on component mount
  useEffect(() => {
    loadLotConfig();
    // Generate initial Signal ID if not set
    if (!data.id) {
      setData(prev => ({ ...prev, id: getOrGenerateSignalId("") }));
    }
  }, []);

  // Event listeners for AI Bot integration
  useEffect(() => {
    const handleSetLongText = (event: CustomEvent) => {
      console.log('TabInput received setLongText event:', event.detail);
      const { text: newText } = event.detail;
      console.log('Setting text in TabInput:', newText.substring(0, 100) + '...');

      // Set the text in both the state and the textarea directly
      setText(newText);

      // Also set the textarea value directly to ensure it appears
      if (textareaRef.current) {
        textareaRef.current.value = newText;
        console.log('Set textarea value directly');
      }

      // Also parse the text immediately after setting it
      setTimeout(() => {
        console.log('Parsing text in TabInput');
        parseLongText(newText);
      }, 200);
    };

    const handleParseLongText = (event: CustomEvent) => {
      const { text } = event.detail;
      parseLongText(text);
    };

    const handleDistributeLots = (event: CustomEvent) => {
      console.log('TabInput received distributeLots event:', event.detail);
      const { parsedData, defaultLot, maxLot, autoSend } = event.detail;

      if (parsedData) {
        console.log('Setting parsed data:', parsedData);
        // Set the parsed data
        setData(parsedData);
        // Distribute lots with default lot size
        distributeLots(defaultLot || 0.1);
      } else if (maxLot) {
        console.log('Distributing lots with maxLot:', maxLot);
        // Just distribute lots with the specified max lot
        distributeLots(maxLot);
      }

      // Auto send if requested
      if (autoSend) {
        setTimeout(() => {
          handleSubmitClick();
        }, 500); // Small delay to ensure state is updated
      }
    };

    window.addEventListener('setLongText', handleSetLongText as EventListener);
    window.addEventListener('parseLongText', handleParseLongText as EventListener);
    window.addEventListener('distributeLots', handleDistributeLots as EventListener);

    return () => {
      window.removeEventListener('setLongText', handleSetLongText as EventListener);
      window.removeEventListener('parseLongText', handleParseLongText as EventListener);
      window.removeEventListener('distributeLots', handleDistributeLots as EventListener);
    };
  }, []);

  const loadLotConfig = async () => {
    // Use the global config directly - no need to fetch from JSON file
    setLotConfig(LOT_DISTRIBUTION_CONFIG);
    setMaxLotPerMessage(LOT_DISTRIBUTION_CONFIG.maxLotPerMessage);
  };

  const handleChange = (index: number, field: keyof Row, value: number) => {
    const updated = [...rows];
    // Keep as number but round to 2 decimal places
    updated[index][field] = value;
    setRows(updated);
  };

  const addRow = () => {
    if (rows.length < 10) {
      setRows([...rows, { vtp: 0.0, lot: 0.01 }]);
    }
  };

  const removeRow = (index: number) => {
    const updated = rows.filter((_, i) => i !== index);
    setRows(updated);
  };

  const distributeLots = (totalLot: number) => {
    const tpCount = rows.length;
    const distributionKey = tpCount as keyof typeof lotConfig.lotDistribution;
    const distribution = lotConfig.lotDistribution[distributionKey] || [100];

    const updatedRows = rows.map((row, index) => {
      const percentage = distribution[index] || 1; // Fallback to 1% if index exceeds distribution array
      let calculatedLot = (totalLot * percentage) / 100;

      // Ensure minimum lot size
      const minLot = (lotConfig as any).minLotSize || 0.01;
      if (calculatedLot < minLot) {
        calculatedLot = minLot;
      }

      return {
        ...row,
        lot: parseFloat(calculatedLot.toFixed(2))
      };
    });

    setRows(updatedRows);
  };

  const distributeLotsAutomatically = () => {
    distributeLots(maxLotPerMessage);
  };

  const handleSubmitClick = () => {
    if (requireConfirmation) {
      setShowConfirmation(true);
    } else {
      // Send immediately without confirmation
      handleDirectSubmit();
    }
  };

  const handleDirectSubmit = () => {
    // const webhookData = formatDataForWebhook();
    sendWebhook("g_input", WEBHOOK_GROUP_2, { rows, ...data });
  };

  const handleConfirmSubmit = () => {
    handleDirectSubmit();
    setShowConfirmation(false);
  };

  const handleCancelSubmit = () => {
    setShowConfirmation(false);
  };

  const parseLongText = (textarea: string) => {
    // setText(textarea);
    const lines = textarea.split("\n").map((line) => line.trim());
    const newRows: Row[] = [];
    let newPriceChoices: string[] = [];

    // Extract or generate Signal ID
    const signalId = getOrGenerateSignalId(textarea);
    setData((prev) => ({ ...prev, id: signalId }));

    lines.forEach((line) => {
      if (line.toLowerCase().startsWith("tp")) {
        const match = line.match(/TP\d\s*:\s*(\d+(\.\d+)?)/i);
        if (match) {
          newRows.push({ vtp: parseFloat(match[1]), lot: 0.01 });
        }
      }
      const slMatch = line.match(/SL\s*:\s*(\d+(\.\d+)?)/i);
      if (slMatch) {
        setData((prev) => ({ ...prev, sl: parseFloat(slMatch[1]) }));
      }

      const symbolMatch = line.match(/Symbol\s*:\s*(\w+)/i);
      if (symbolMatch) {
        setData((prev) => ({ ...prev, s: symbolMatch[1].toUpperCase() }));
      }

      const signalMatch = line.match(/Signal\s*:\s*(.+)/i);
      if (signalMatch) {
        const rawAction = signalMatch[1].trim();
        const mappedAction = mapActionToValid(rawAction);
        setData((prev) => ({ ...prev, a: mappedAction }));
      }

      const priceMatch = line.match(/Price\s*:\s*(\d+(\.\d+)?)(\s*[-–]\s*(\d+(\.\d+)?))?/i);
      if (priceMatch) {
        const start = parseFloat(priceMatch[1]);
        const end = parseFloat(priceMatch[4]);
        let mid = "0";
        if (!isNaN(start) && !isNaN(end)) {
          mid = formatNumberBySymbol((start + end) / 2, data.s);
          newPriceChoices = [formatNumberBySymbol(start, data.s), mid, formatNumberBySymbol(end, data.s)];
        } else if (!isNaN(start)) {
          mid = formatNumberBySymbol(start, data.s);
          newPriceChoices = [mid];
        }
        setPriceChoices(newPriceChoices);
        setData((prev) => ({ ...prev, p: parseFloat(mid) }));
      }

      const commentMatch = line.replace(/[&+-\s \(\)]/g, '').match(/C\.(\w+)/i);
      if (commentMatch) {
        setData((prev) => ({ ...prev, c: `ZD_${commentMatch[1].slice(0, 3)}` }));
        // setData((prev) => ({ ...prev, c: `ZD_C_${commentMatch[1]}_${signalId}` }));
      }

      const reasonMatch = line.match(/Reason\s*:\s*(.+)/i);
      if (reasonMatch) {
        setData((prev) => ({ ...prev, reason: reasonMatch[1].trim() }));
      }

      const riskMatch = line.match(/Risk\s*:\s*(.+)/i);
      if (riskMatch) {
        setData((prev) => ({ ...prev, risk: riskMatch[1].trim() }));
      }
    });

    // Update comment to include Signal ID if not already set by C. pattern
    if (!textarea.match(/C\.\w+/i)) {
      setData((prev) => ({ ...prev, c: `IN_MANUAL_${signalId}` }));
    }

    if (newRows.length > 0) {
      const limitedRows = newRows.slice(0, 10);
      setRows(limitedRows);

      // Auto-apply lot distribution after parsing
      // setTimeout(() => {
      //   distributeLotsAutomatically();
      // }, 100);

      // setSymbolPoint(SYMBOL_OPTIONS.find((opt) => opt.value === data.s)?.point || 0.01); 
    }
  };

  const copyToClipboard = async () => {
    try {
      if (!navigator.clipboard || !navigator.clipboard.writeText) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showSuccess("Text copied to clipboard!");
        return;
      }

      await navigator.clipboard.writeText(text);
      showSuccess("Text copied to clipboard!");
    } catch (err) {
      console.error("Failed to copy text: ", err);
      showError("Failed to copy text to clipboard. Please select and copy the text manually.");
    }
  };

  const pasteFromClipboard = async () => {
    try {
      // Check if clipboard API is available
      if (!navigator.clipboard || !navigator.clipboard.readText) {
        showError("Clipboard API not supported. Please paste manually using Ctrl+V in the textarea.");
        return;
      }

      // Check permissions
      const permission = await navigator.permissions.query({ name: 'clipboard-read' as PermissionName });
      if (permission.state === 'denied') {
        showError("Clipboard access denied. Please paste manually using Ctrl+V in the textarea.");
        return;
      }

      const clipboardText = await navigator.clipboard.readText();
      if (clipboardText) {
        setText(clipboardText);
        parseLongText(clipboardText);
        // alert("Text pasted and parsed successfully!");
      } else {
        showError("No text found in clipboard");
      }
    } catch (err) {
      console.error("Failed to paste text: ", err);
      // Provide user-friendly error message with alternative
      showError("Failed to paste from clipboard. This might be due to browser security restrictions. Please paste manually using Ctrl+V in the textarea above.");
    }
  };

  const clearAllData = () => {
    setText("");
    // Reset TP rows to default
    setRows([{ vtp: 0.0, lot: 0.01 }]);
    // Reset data to default values
    setData(prev => ({
      ...prev,
      p: 0.0,
      vsl: 0.0,
      id: ""
    }));
    setPriceChoices([]);
    showSuccess("All data cleared successfully!");
  };

  // Calculate profit/loss for TP or SL
  const calculateProfitLoss = (targetPrice: number, lotSize: number): number => {
    if (data.p === 0 || targetPrice === 0) return 0;

    const entryPrice = data.p;
    const cleanSymbol = data.s.split('.')[0]; // Clean symbol by removing suffixes like .s, .m, etc.
    const isBuyAction = data.a.toLowerCase().includes('buy');
    const symbolPoint = getSymbolPoint(cleanSymbol);

    // Calculate price difference in points
    let priceDiff: number;
    if (isBuyAction) {
      priceDiff = targetPrice - entryPrice; // Positive for profit, negative for loss
    } else {
      priceDiff = entryPrice - targetPrice; // Positive for profit, negative for loss
    }

    // Convert to points and calculate profit/loss
    const pointsProfit = priceDiff / symbolPoint;

    // Standard lot value calculation (varies by symbol type)
    // Point value represents the dollar value per point per mini lot
    let pointValue: number;
    if (cleanSymbol.includes('JPY')) {
      pointValue = 0.01; // For JPY pairs, 1 point = $0.01 per mini lot
    } else if (cleanSymbol === 'XAUUSD') {
      pointValue = 0.01; // For Gold, 1 point = $0.01 per mini lot
    } else if (cleanSymbol.includes('USD')) {
      pointValue = 0.0001; // For major USD pairs, 1 point = $0.0001 per mini lot
    } else {
      pointValue = 0.0001; // Default for other pairs
    }

    // Calculate profit/loss in USD
    const profitLoss = pointsProfit * pointValue * (lotSize * 100); // lotSize is in mini lots

    return profitLoss;
  };

  const computeTPValues = () => {
    if (data.p === 0 || data.vsl === 0) {
      showError("Please enter Entry Price and SL before computing TP values");
      return;
    }

    const entryPrice = data.p;
    const stopLoss = data.vsl;
    const pointsSL = Math.abs(entryPrice - stopLoss);
    const isBuyAction = data.a.toLowerCase().includes('buy');

    // Get symbol point for proper calculation
    const symbolPoint = getSymbolPoint(data.s);

    // Calculate TP values based on action type
    const newRows = rows.map((row, index) => {
      const multiplier = index + 1;
      let tpValue: number;

      if (isBuyAction) {
        // For Buy actions: TP = Entry + (points * multiplier)
        tpValue = entryPrice + (pointsSL * multiplier);
      } else {
        // For Sell actions: TP = Entry - (points * multiplier)
        tpValue = entryPrice - (pointsSL * multiplier);
      }

      // Round to appropriate decimal places based on symbol point
      const decimals = symbolPoint >= 1 ? 0 : symbolPoint >= 0.01 ? 2 : symbolPoint >= 0.001 ? 3 : 5;
      tpValue = parseFloat(tpValue.toFixed(decimals));

      return { ...row, vtp: tpValue };
    });

    setRows(newRows);
    showSuccess(`TP values computed successfully! Points SL: ${pointsSL.toFixed(5)}`);
  };

  const priceOptions = priceChoices.map((p) => ({ label: p, value: p }));

  return (
    <div className="space-y-4 w-full max-w-full overflow-hidden">
      {/* Long Text */}
      <div>
        <label className="block text-sm font-medium text-gray-200">
          Long Text <span className="text-gray-400 text-xs">(Ctrl+V)</span>
        </label>
        <div className="relative">
          <textarea
            ref={textareaRef}
            value={text}
            onChange={(e) => { setText(e.target.value); parseLongText(e.target.value); }}
            placeholder="Paste your trading signal here..."
            rows={6}
            className="mt-1 w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2 pr-20"
          />
          <FloatingTextareaButtons
            onCopy={copyToClipboard}
            onPaste={pasteFromClipboard}
            onClear={clearAllData}
          />
        </div>
      </div>

      {/* Dynamic Rows A / B */}
      {rows.map((row, index) => {
        const tpProfit = calculateProfitLoss(row.vtp, row.lot);
        const tpLoss = calculateProfitLoss(data.vsl, row.lot);

        // Calculate RR for this TP
        const rrRatio = data.vsl > 0 && row.vtp > 0 && data.p > 0 ?
          calculateRiskReward(data.p, data.vsl, row.vtp, data.a) : "-";

        return (
          <div
            key={index}
            className="flex flex-col sm:flex-row grid grid-cols-2 lg:grid-cols-2 gap-4 items-center"
          >
            <div className="w-full">
              <label className="block text-sm font-medium text-gray-200">
                TP{index + 1}
              </label>
              <input
                type="text"
                placeholder={`TP${index + 1}`}
                className="mt-1 w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
                value={formatNumberBySymbol(row.vtp, data.s)}
                onChange={(e) => handleChange(index, "vtp", parseFloat(formatNumberBySymbol(Number(e.target.value), data.s)))}
              />
              {data.vsl && row.vtp ? (
                <small className="text-xs font-mono text-nowrap">
                  {rrRatio !== "-" && (
                    <small className="text-yellow-400">
                      RR: {rrRatio}
                    </small>
                  )}
                  &nbsp;
                  <> 
                    (<small className="text-yellow-400 mx-1">
                        {Math.round(Math.abs(data.p - row.vtp) / getSymbolPoint(data.s))} <small>Pts</small>
                      </small>)
                  </>
                </small>
              ) : ""}
            </div>
            <div className="w-full">
              <label className="block text-sm font-medium text-gray-200">
                Lot &nbsp;
              </label>
              <div className="flex gap-2 items-center">
                <input
                  type="text"
                  className="mt-1 w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
                  value={row.lot}
                  onChange={(e) => handleChange(index, "lot", parseFloat(e.target.value))}
                />
                {rows.length > 1 && (
                  <button
                    onClick={() => removeRow(index)}
                    className="text-red-400 hover:text-red-600 text-xl"
                  >
                    ✖
                  </button>
                )}
              </div>
              
              <small className="flex gap-2 items-center text-xs text-nowrap">
                {row.vtp > 0 && data.p > 0 && (
                  <span className="mt-1">
                    {formatProfitLoss(tpProfit, 1, settings.currency.showThb, settings.currency.usdToThbRate)} /&nbsp;
                    {formatProfitLoss(tpLoss, 1, settings.currency.showThb, settings.currency.usdToThbRate)}
                  </span>
                )}
              </small>
            </div>
          </div>
        );
      })}

      {/* Add Row Button */}
      {/* <div> */}

      <div className="grid grid-cols-3 ">

        <div className="flex gap-2">
          <button
            onClick={addRow}
            disabled={rows.length >= 10}
            className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 disabled:opacity-50"
          >
            ➕ TP
          </button>
          <button
            onClick={computeTPValues}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            title="Calculate TP values based on Entry Price and SL"
          >
            🧮
          </button>
        </div>
        <div className="col-span-2 text-right">
          {/* <span className="ml-4">Max Lot</span> */}
          <button
            onClick={distributeLotsAutomatically}
            className="py-2 px-4 ml-4 bg-sky-600 text-white font-semibold rounded-md hover:bg-blue-700"
            title="Automatically distribute lots based on TP count"
          >
            Distribute Lots ⋙ {/*⋘ ⋙*/}
          </button>
          <input
            type="number"
            step="0.01"
            min="0.01"
            value={maxLotPerMessage}
            onChange={(e) => setMaxLotPerMessage(parseFloat(e.target.value) || 0.01)}
            className="w-1/6 ml-2 mt-1 text-center rounded-md bg-gray-800 text-white border border-gray-600 p-2"
            placeholder="0.1"
          />
        </div>
      </div>
      {/* Distribution Logic (Same as Discord Bot):
        TP Count	Distribution
        1 TP	100%
        2 TPs	70%, 30%
        3 TPs	70%, 20%, 10%
        4 TPs	65%, 20%, 10%, 5%
        5 TPs	50%, 25%, 15%, 9%, 1% 
      */}

      {/* Smart Lot Distribution */}
      <div className="flex flex-col sm:flex-row grid grid-cols-1 lg:grid-cols-1 gap-4 text-right mb-4">

        <div className="mb-2 text-xs text-gray-400">
          {rows.length} TP{rows.length !== 1 ? 's' : ''}: {
            (() => {
              const distributionKey = rows.length as keyof typeof lotConfig.lotDistribution;
              const distribution = lotConfig.lotDistribution[distributionKey] || [100];
              return distribution.slice(0, rows.length).join('%, ') + '%';
            })()
          }
        </div>
      </div>

      <div className="flex flex-col sm:flex-row grid grid-cols-2 lg:grid-cols-2 gap-4 items-center">

        {/* Symbol */}
        {/* <div className="w-full sm:w-1/2"> */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">Symbol</label>
          <Select
            className="text-black"
            value={SYMBOL_OPTIONS.find((opt) => opt.value === data.s)}
            onChange={(e) => setData({ ...data, s: e?.value || "" })}
            options={SYMBOL_OPTIONS}
            isSearchable
            styles={customStyle}
          />
        </div>

        {/* Signal Type */}
        {/* <div className="w-full sm:w-1/2"> */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">
            Action
          </label>
          <select
            value={data.a}
            onChange={(e) => setData({ ...data, a: e.target.value })}
            className="w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
          >
            <option value="Sell Limit">Sell Limit</option>
            <option value="Buy Limit">Buy Limit</option>
          </select>
        </div>
      </div>
      <div className="flex flex-col sm:flex-row grid grid-cols-2 lg:grid-cols-2 gap-4 items-center">

        {/* Price Select */}
        {/* <div className="w-full sm:w-1/2"> */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">Entry Price</label>
          <Select
            options={priceOptions}
            value={
              priceOptions.find((opt) => parseFloat(opt.value) === data.p) || {
                label: formatNumberBySymbol(data.p, data.s),
                value: formatNumberBySymbol(data.p, data.s),
              }
            }
            onChange={(selected) =>
              setData((prev) => ({
                ...prev,
                p: parseFloat(selected?.value || "0"),
              }))
            }
            onInputChange={(inputValue) => {
              const floatVal = parseFloat(inputValue);
              if (!isNaN(floatVal)) {
                setData((prev) => ({
                  ...prev,
                  p: parseFloat(formatNumberBySymbol(floatVal, data.s)),
                }));
              }
            }}
            isClearable
            isSearchable
            placeholder="เลือกราคาหรือพิมพ์เอง"
            className="text-black"
            styles={customStyle}
          />
        </div>

        {/* SL */}
        {/* <div className="w-full sm:w-1/2"> */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">
            SL &nbsp;
            {data.vsl > 0 && data.p > 0 && (() => {
              // Calculate total lot size for SL calculation
              const totalLot = rows.reduce((sum, row) => sum + row.lot, 0);
              const slLoss = calculateProfitLoss(data.vsl, totalLot);

              return (
                <span className="mt-1 text-xs text-nowrap">
                  <span className=" text-gray-400">
                    All Lot: {totalLot.toFixed(2)}
                  </span> &nbsp;
                  {formatProfitLoss(slLoss, 1, settings.currency.showThb, settings.currency.usdToThbRate)}
                </span>
              );
            })()}
          </label>
          <input
            type="text"
            className="w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
            value={formatNumberBySymbol(data.vsl, data.s)}
            onChange={(e) => setData({ ...data, vsl: parseFloat(formatNumberBySymbol(Number(e.target.value), data.s)) })}
          />

        </div>
      </div>

      {/* Signal ID and Comment in one row */}
      <div className="flex flex-col sm:flex-row grid grid-cols-1 lg:grid-cols-2 gap-4 items-center">
        {/* Signal ID */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">Signal ID</label>
          <div className="flex gap-2">
            <input
              type="text"
              className="mt-1 flex-1 rounded-md bg-gray-800 text-white border border-gray-600 p-2"
              value={data.id}
              onChange={(e) => setData({ ...data, id: e.target.value })}
              placeholder="Auto-generated or extracted from text"
            />
            {/* <button
              type="button"
              onClick={() => setData(prev => ({ ...prev, id: getOrGenerateSignalId("") }))}
              className="mt-1 px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              title="Generate new Signal ID"
            >
              🔄
            </button> */}
          </div>
        </div>

        {/* Comment */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">Comment</label>
          <input
            type="text"
            className="mt-1 w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
            value={data.c}
            onChange={(e) => setData({ ...data, c: e.target.value })}
          />
        </div>
      </div>

      {/* Reason */}
      <div>
        <label className="block text-sm font-medium text-gray-200">Reason</label>
        <textarea
          className="mt-1 w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
          value={data.reason}
          onChange={(e) => setData({ ...data, reason: e.target.value })}
          placeholder="Trading reason or analysis"
          rows={3}
        />
      </div>

      {/* Risk */}
      <div>
        <label className="block text-sm font-medium text-gray-200">Risk</label>
        <textarea
          className="mt-1 w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
          value={data.risk}
          onChange={(e) => setData({ ...data, risk: e.target.value })}
          placeholder="Risk assessment and considerations (parsed from Risk: field)"
          rows={3}
        />
      </div>

      {/* Submit */}
      <button
        disabled={loading}
        onClick={handleSubmitClick}
        className="w-full py-2 px-4 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700 disabled:opacity-50"
      >
        {loading ? "⏳ Sending..." : "🚀 Send"}
      </button>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showConfirmation}
        title="Confirm Webhook Send"
        message={`Are you sure you want to send ${data.a} for ${data.s}?`}
        confirmText="Send Webhook"
        cancelText="Cancel"
        onConfirm={handleConfirmSubmit}
        onCancel={handleCancelSubmit}
        type="warning"
        data={`At price ${data.p} with SL ${data.vsl} and ${rows.length} TP(s)`}
      // data={{
      //   action: data.a,
      //   symbol: data.s,
      //   price: data.p,
      //   stopLoss: data.vsl,
      //   comment: data.c,
      //   rows: rows
      // }}
      />

      {/* Alert Dialog */}
      <AlertDialog
        isOpen={alert.isOpen}
        title={alert.title}
        message={alert.message}
        type={alert.type}
        onClose={closeAlert}
        autoClose={alert.autoClose}
      />
    </div>
  );
}
